/**
 * 表格预设数据配置
 * 用于JSON表格演示页面的预设数据管理
 */

export const TABLE_PRESETS = {
  basic: {
    name: '基础数据',
    description: '简单的表格数据示例',
    data: {
      rows: [
        ["检查项目1", "技术要求1", "合格", "12", "15", "张三", "李四", "王五"],
        ["检查项目2", "技术要求2", "合格", "12", "16", "张三", "李四", "王五"],
        ["检查项目3", "技术要求3", "不合格", "12", "17", "张三", "李四", "王五"]
      ]
    }
  },

  merged: {
    name: '合并单元格',
    description: '包含合并单元格的表格示例',
    data: {
      rows: [
        ["外观检查", "表面无划痕、无变形", "合格", "12", "15", "张三", "李四", "王五"],
        ["", "尺寸符合图纸要求", "合格", "12", "16", "张三", "李四", "王五"],
        ["功能测试", "各项功能正常", "合格", "12", "17", "张三", "李四", "王五"]
      ],
      merges: [
        {
          startRow: 0,
          startCol: 0,
          endRow: 1,
          endCol: 0,
          content: "外观检查"
        }
      ]
    }
  },

  complex: {
    name: '复杂示例',
    description: '包含数学公式和复杂合并的示例',
    data: {
      rows: [
        ["数学公式测试", "$E = mc^2$", "合格", "12", "15", "张三", "李四", "王五"],
        ["几何计算", "$\\pi r^2$", "合格", "12", "16", "张三", "李四", "王五"],
        ["统计分析", "$\\sum_{i=1}^n x_i$", "合格", "12", "17", "张三", "李四", "王五"],
        ["积分计算", "$\\int_0^1 x dx = \\frac{1}{2}$", "合格", "12", "18", "张三", "李四", "王五"]
      ],
      merges: [
        {
          startRow: 2,
          startCol: 3,
          endRow: 3,
          endCol: 4,
          content: "12月17-18日"
        }
      ]
    }
  },

  customHeader: {
    name: '自定义表头',
    description: '包含自定义表头配置的复杂示例',
    data: {
      // 自定义表头配置
      headerConfig: {
        headers: [
          ['产品名称', '规格型号', '质量检验', '生产信息', '', '责任人员', '', ''],
          ['', '', '', '批次号', '日期', '检验员', '审核员', '负责人']
        ],
        merges: [
          // 产品名称 - 纵向合并
          {startRow: 0, startCol: 0, endRow: 1, endCol: 0, content: '产品名称'},
          // 规格型号 - 纵向合并
          {startRow: 0, startCol: 1, endRow: 1, endCol: 1, content: '规格型号'},
          // 质量检验 - 纵向合并
          {startRow: 0, startCol: 2, endRow: 1, endCol: 2, content: '质量检验'},
          // 生产信息 - 横向合并
          {startRow: 0, startCol: 3, endRow: 0, endCol: 4, content: '生产信息'},
          // 责任人员 - 横向合并
          {startRow: 0, startCol: 5, endRow: 0, endCol: 7, content: '责任人员'}
        ]
      },
      // 自定义表头宽度配置
      headerWidthConfig: {
        columnWidths: [180, 150, 120, 100, 100, 90, 90, 90], // 8列的宽度
        headerHeights: [60, 40] // 两行表头的高度
      },
      // 数据行（使用复杂格式支持公式和宽度）
      cellRows: [
        [
          { content: "智能手机", width: 180, height: 50 },
          { content: "iPhone 15 Pro", width: 150, height: 50 },
          { content: "外观检查：无划痕", width: 120, height: 50 },
          { content: "A001", width: 100, height: 50 },
          { content: "2024-01-15", width: 100, height: 50 },
          { content: "张三", width: 90, height: 50 },
          { content: "李四", width: 90, height: 50 },
          { content: "王五", width: 90, height: 50 }
        ],
        [
          { content: "", width: 180, height: 50 },
          { content: "", width: 150, height: 50 },
          { content: "功能测试：正常", width: 120, height: 50 },
          { content: "A001", width: 100, height: 50 },
          { content: "2024-01-15", width: 100, height: 50 },
          { content: "张三", width: 90, height: 50 },
          { content: "李四", width: 90, height: 50 },
          { content: "王五", width: 90, height: 50 }
        ],
        [
          { content: "平板电脑", width: 180, height: 50 },
          { content: "iPad Air", width: 150, height: 50 },
          { content: "性能测试：$CPU = 95\\%$", hasMath: true, width: 120, height: 60 },
          { content: "B002", width: 100, height: 50 },
          { content: "2024-01-16", width: 100, height: 50 },
          { content: "赵六", width: 90, height: 50 },
          { content: "钱七", width: 90, height: 50 },
          { content: "孙八", width: 90, height: 50 }
        ]
      ],
      // 数据行合并配置
      merges: [
        {
          startRow: 0,
          startCol: 0,
          endRow: 1,
          endCol: 0,
          content: "智能手机\n（多功能检测）"
        }
      ],
      // 元数据
      metadata: {
        title: "产品质量检验记录表",
        useDynamicHeader: true,
        hasCustomWidth: true
      }
    }
  },

  // 新增示例数据
  test: {
    name: '测试json表头',
    description: '包含json表头配置的复杂示例',
    data: {
      // 自定义表头配置
      headerConfig: {
        headers: [
          ['产品名称', '生产信息', '', '责任人员', '', ''],
          ['', '批次号', '日期', '检验员', '审核员', '负责人']
        ],
        merges: [
          // 产品名称 - 纵向合并
          {startRow: 0, startCol: 0, endRow: 1, endCol: 0, content: '产品名称'},
          // 生产信息 - 横向合并
          {startRow: 0, startCol: 1, endRow: 0, endCol: 2, content: '生产信息'},
          // 责任人员 - 横向合并
          {startRow: 0, startCol: 3, endRow: 0, endCol: 5, content: '责任人员'}
        ]
      },
      // 自定义表头宽度配置
      headerWidthConfig: {
        columnWidths: [180, 100, 100, 80, 80, 80], // 8列的宽度
        headerHeights: [60, 40], // 两行表头的高度
        verticalHeaders: [false, false, false, true, true, true]
      },
      // 数据行（使用复杂格式支持公式和宽度）
      cellRows: [
        [
          { content: "智能手机", width: 180, height: 50 },
          { content: "A001", width: 100, height: 50 },
          { content: "2024-01-15", width: 100, height: 50 },
          { content: "张三", width: 90, height: 50 },
          { content: "李四", width: 90, height: 50 },
          { content: "王五", width: 90, height: 50 }
        ]
      ],
      // 数据行合并配置
      merges: [
        {
          startRow: 0,
          startCol: 0,
          endRow: 1,
          endCol: 0,
          content: "智能手机\n（多功能检测）"
        }
      ],
      // 元数据
      metadata: {
        title: "检验记录表",
        useDynamicHeader: true,
        hasCustomWidth: true
      }
    }
  }
}

/**
 * 获取所有预设数据的选项列表
 * @returns {Array} 预设数据选项数组
 */
export function getPresetOptions() {
  return Object.keys(TABLE_PRESETS).map(key => ({
    value: key,
    label: TABLE_PRESETS[key].name,
    description: TABLE_PRESETS[key].description
  }))
}

/**
 * 根据键获取预设数据
 * @param {string} key 预设数据键
 * @returns {Object|null} 预设数据对象
 */
export function getPresetData(key) {
  return TABLE_PRESETS[key]?.data || null
}

/**
 * 添加新的预设数据
 * @param {string} key 预设数据键
 * @param {Object} preset 预设数据对象
 */
export function addPresetData(key, preset) {
  if (!key || !preset) {
    throw new Error('键和预设数据都是必需的')
  }

  if (TABLE_PRESETS[key]) {
    console.warn(`预设数据 "${key}" 已存在，将被覆盖`)
  }

  TABLE_PRESETS[key] = {
    name: preset.name || key,
    description: preset.description || '',
    data: preset.data || {}
  }
}

/**
 * 删除预设数据
 * @param {string} key 预设数据键
 * @returns {boolean} 删除是否成功
 */
export function removePresetData(key) {
  if (TABLE_PRESETS[key]) {
    delete TABLE_PRESETS[key]
    return true
  }
  return false
}

/**
 * 检查预设数据是否存在
 * @param {string} key 预设数据键
 * @returns {boolean} 是否存在
 */
export function hasPresetData(key) {
  return !!TABLE_PRESETS[key]
}

export default {
  TABLE_PRESETS,
  getPresetOptions,
  getPresetData,
  addPresetData,
  removePresetData,
  hasPresetData
}
