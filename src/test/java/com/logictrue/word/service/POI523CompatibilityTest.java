package com.logictrue.word.service;

import org.apache.poi.xwpf.usermodel.XWPFDocument;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Arrays;

/**
 * POI 5.2.3兼容性测试
 * 验证SimpleWordService在POI 5.2.3版本下的兼容性
 */
public class POI523CompatibilityTest {

    public static void main(String[] args) {
        System.out.println("🔍 POI 5.2.3兼容性测试开始...");
        
        // 检查POI版本
        checkPOIVersion();
        
        try {
            // 测试基本功能
            testBasicFunctionality();
            
            // 测试复杂表格
            testComplexTable();
            
            System.out.println("✅ 所有测试通过！POI 5.2.3兼容性良好");
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 检查POI版本
     */
    private static void checkPOIVersion() {
        try {
            // 尝试创建一个简单的文档来验证POI是否正常工作
            XWPFDocument doc = new XWPFDocument();
            doc.createParagraph().createRun().setText("POI版本测试");
            doc.close();
            
            System.out.println("📦 POI库加载成功");
            System.out.println("🔧 使用版本: Apache POI 5.2.3");
            
        } catch (Exception e) {
            System.err.println("❌ POI库加载失败: " + e.getMessage());
            throw new RuntimeException("POI库不兼容", e);
        }
    }

    /**
     * 测试基本功能
     */
    private static void testBasicFunctionality() throws IOException {
        System.out.println("\n🧪 测试1: 基本功能测试");
        
        SimpleWordService wordService = new SimpleWordService();
        SimpleTableConfig config = createBasicConfig();
        
        byte[] wordBytes = wordService.exportWord(config);
        
        String fileName = "poi523_basic_test.docx";
        try (FileOutputStream fos = new FileOutputStream(fileName)) {
            fos.write(wordBytes);
        }
        
        System.out.println("✅ 基本功能测试通过");
        System.out.println("📁 生成文件: " + fileName);
        System.out.println("📊 文件大小: " + wordBytes.length + " bytes");
    }

    /**
     * 测试复杂表格
     */
    private static void testComplexTable() throws IOException {
        System.out.println("\n🧪 测试2: 复杂表格测试");
        
        SimpleWordService wordService = new SimpleWordService();
        SimpleTableConfig config = createComplexConfig();
        
        byte[] wordBytes = wordService.exportWord(config);
        
        String fileName = "poi523_complex_test.docx";
        try (FileOutputStream fos = new FileOutputStream(fileName)) {
            fos.write(wordBytes);
        }
        
        System.out.println("✅ 复杂表格测试通过");
        System.out.println("📁 生成文件: " + fileName);
        System.out.println("📊 文件大小: " + wordBytes.length + " bytes");
    }

    /**
     * 创建基本配置
     */
    private static SimpleTableConfig createBasicConfig() {
        SimpleTableConfig config = new SimpleTableConfig();
        config.setTitle("POI 5.2.3基本功能测试");
        config.setLandscape(true);
        
        // 简单的2x4表格
        config.setColumnWidths(Arrays.asList(100, 100, 100, 100));
        config.setHeaderHeights(Arrays.asList(50));
        
        config.addHeaderRow(Arrays.asList("列1", "列2", "列3", "列4"));
        config.addDataRow(Arrays.asList("数据1", "数据2", "数据3", "数据4"));
        
        return config;
    }

    /**
     * 创建复杂配置
     */
    private static SimpleTableConfig createComplexConfig() {
        SimpleTableConfig config = new SimpleTableConfig();
        config.setTitle("POI 5.2.3复杂表格测试");
        config.setLandscape(true);
        
        // 复杂的3行6列表格
        config.setColumnWidths(Arrays.asList(120, 100, 90, 90, 80, 120));
        config.setHeaderHeights(Arrays.asList(60, 45, 40));
        
        // 三行表头
        config.addHeaderRow(Arrays.asList("主标题1", "", "主标题2", "", "主标题3", ""));
        config.addHeaderRow(Arrays.asList("子标题1", "子标题2", "子标题3", "子标题4", "子标题5", "子标题6"));
        config.addHeaderRow(Arrays.asList("详细1", "详细2", "详细3", "详细4", "详细5", "详细6"));
        
        // 多行数据
        for (int i = 1; i <= 3; i++) {
            config.addDataRow(Arrays.asList(
                "数据" + i + "1", "数据" + i + "2", "数据" + i + "3", 
                "数据" + i + "4", "数据" + i + "5", "数据" + i + "6"
            ));
        }
        
        // 复杂合并
        config.addMerge(0, 0, 0, 1, "主标题1", true);  // 跨2列
        config.addMerge(0, 2, 0, 3, "主标题2", true);  // 跨2列
        config.addMerge(0, 4, 2, 4, "主标题3", true);  // 跨3行
        config.addMerge(1, 0, 1, 1, "合并数据", false); // 数据合并
        
        return config;
    }
}

/**
 * POI 5.2.3特性测试
 */
class POI523FeatureTest {
    
    /**
     * 测试POI 5.2.3的新特性和变化
     */
    public static void testPOI523Features() {
        System.out.println("🔬 POI 5.2.3特性测试:");
        
        try {
            // 测试文档创建
            XWPFDocument doc = new XWPFDocument();
            System.out.println("✅ 文档创建: 正常");
            
            // 测试段落创建
            doc.createParagraph().createRun().setText("测试文本");
            System.out.println("✅ 段落创建: 正常");
            
            // 测试表格创建
            doc.createTable(2, 3);
            System.out.println("✅ 表格创建: 正常");
            
            doc.close();
            System.out.println("✅ 文档关闭: 正常");
            
        } catch (Exception e) {
            System.err.println("❌ POI 5.2.3特性测试失败: " + e.getMessage());
        }
    }
}
